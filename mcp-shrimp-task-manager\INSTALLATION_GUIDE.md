# MCP Shrimp Task Manager 安装和使用指南

## 📋 概述

MCP Shrimp Task Manager 是一个基于 Model Context Protocol (MCP) 的智能任务管理系统，专为 AI Agents 设计，提供高效的编程工作流框架。

## 🚀 安装步骤

### 1. 环境要求
- Node.js 16+ 
- npm 或 yarn
- Git

### 2. 克隆和安装
```bash
# 克隆项目
git clone https://github.com/cjo4m06/mcp-shrimp-task-manager.git
cd mcp-shrimp-task-manager

# 安装依赖
npm install

# 构建项目
npm run build
```

### 3. 配置环境变量
复制 `.env.example` 到 `.env` 并修改配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
DATA_DIR=d:\IdeaProjects\flying_service_plus\mcp-shrimp-task-manager\shrimp-data
TEMPLATES_USE=en
ENABLE_GUI=false
```

## 🔧 Cursor IDE 配置

### 全局配置
在 `~/.cursor/mcp.json` 中添加：
```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["d:\\IdeaProjects\\flying_service_plus\\mcp-shrimp-task-manager\\dist\\index.js"],
      "env": {
        "DATA_DIR": "d:\\IdeaProjects\\flying_service_plus\\mcp-shrimp-task-manager\\shrimp-data",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

### 项目特定配置
在项目根目录创建 `.cursor/mcp.json`：
```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "node",
      "args": ["d:\\IdeaProjects\\flying_service_plus\\mcp-shrimp-task-manager\\dist\\index.js"],
      "env": {
        "DATA_DIR": ".shrimp",
        "TEMPLATES_USE": "en",
        "ENABLE_GUI": "false"
      }
    }
  }
}
```

## 🎯 使用方法

### 基本命令
- **初始化项目规则**: `init project rules`
- **规划任务**: `plan task [任务描述]`
- **执行任务**: `execute task [任务名称或ID]`
- **列出任务**: `list tasks`
- **研究模式**: `research [主题]`
- **连续模式**: `continuous mode`

### 工作流程
1. **项目初始化**: 首次使用时运行 `init project rules`
2. **任务规划**: 使用 `plan task` 创建详细的任务计划
3. **任务执行**: 使用 `execute task` 执行具体任务
4. **进度跟踪**: 使用 `list tasks` 查看任务状态

## 🛠️ 可用工具

| 工具类别 | 工具名称 | 描述 |
|---------|---------|------|
| **任务规划** | `plan_task` | 开始规划任务 |
| **任务分析** | `analyze_task` | 深入分析任务需求 |
| **解决方案评估** | `reflect_task` | 反思和改进解决方案概念 |
| **研究调查** | `research_mode` | 进入系统化技术研究模式 |
| **项目管理** | `init_project_rules` | 初始化或更新项目标准和规则 |
| **任务管理** | `split_tasks` | 将任务分解为子任务 |
| **任务执行** | `execute_task` | 执行特定任务 |

## 🔍 测试安装

运行以下命令测试安装：
```bash
node dist/index.js
```

如果看到服务器启动但没有错误信息，说明安装成功。

## 💡 使用建议

### Cursor IDE 自定义模式
在 Cursor Settings => Features => Custom modes 中配置：

**TaskPlanner 模式**:
```
You are a professional task planning expert. You must interact with users, analyze their needs, and collect project-related information. Finally, you must use "plan_task" to create tasks. When the task is created, you must summarize it and inform the user to use the "TaskExecutor" mode to execute the task.
```

**TaskExecutor 模式**:
```
You are a professional task execution expert. When a user specifies a task to execute, use "execute_task" to execute the task.
If no task is specified, use "list_tasks" to find unexecuted tasks and execute them.
```

## 🚨 故障排除

### 常见问题
1. **路径问题**: 确保所有路径使用绝对路径
2. **权限问题**: 确保有写入 DATA_DIR 的权限
3. **Node.js 版本**: 确保使用 Node.js 16+

### 日志查看
检查 `shrimp-data` 目录下的日志文件获取详细错误信息。

## 📚 更多资源

- [GitHub 项目](https://github.com/cjo4m06/mcp-shrimp-task-manager)
- [官方文档](https://cjo4m06.github.io/mcp-shrimp-task-manager/)
- [MCP 协议文档](https://modelcontextprotocol.io/)

## 🎉 安装完成

恭喜！MCP Shrimp Task Manager 已成功安装。现在你可以在支持 MCP 的客户端（如 Cursor IDE）中使用这个强大的任务管理工具了。
