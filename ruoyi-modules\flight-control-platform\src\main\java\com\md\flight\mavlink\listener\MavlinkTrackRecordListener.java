package com.md.flight.mavlink.listener;

import com.md.domain.bo.FlightTrackPointBo;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTrackService;
import com.md.utils.TrackRecordRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MAVLink轨迹记录监听器
 * 监听MAVLink无人机状态变化，在任务执行期间将轨迹数据保存到MongoDB
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MavlinkTrackRecordListener {

    private final IFlightTrackService flightTrackService;
    private final MavlinkCoreService mavlinkCoreService;
    private final TrackRecordRedisUtils trackRecordRedisUtils;

    // 最小记录间隔（毫秒）
    private static final long MIN_RECORD_INTERVAL = 950;

    /**
     * 监听任务执行事件（仅处理MAVLink类型的事件）
     *
     * @param event 任务执行事件
     */
    @EventListener
    public void onTaskExecutionEvent(TaskExecutionEvent event) {
        // 只处理MAVLink类型的事件
        if (event.getDroneType() != DroneType.MAVLINK) {
            log.debug("忽略非MAVLink类型的任务执行事件: droneId={}, droneType={}", event.getDroneId(),
                event.getDroneType());
            return;
        }

        String droneId = event.getDroneId();

        if (event.isExecuting()) {
            // 任务开始执行，记录任务ID
            trackRecordRedisUtils.setActiveTask("MAVLINK", droneId, event.getTaskId());
            log.info("开始记录MAVLink无人机[{}]的轨迹，任务ID：{}, droneType={}", droneId, event.getTaskId(),
                event.getDroneType());
        } else {
            // 任务结束，移除记录
            trackRecordRedisUtils.removeActiveTask("MAVLINK", droneId);
            trackRecordRedisUtils.removeLastRecordTime("MAVLINK", droneId);
            // 注意：不调用 removeTaskStartTime，因为没有使用开始时间
            log.info("停止记录MAVLink无人机[{}]的轨迹, droneType={}", droneId, event.getDroneType());
        }
    }

    /**
     * 定期检查活动任务，记录轨迹点
     * 每秒执行一次，检查所有执行中的任务
     */
    @Scheduled(fixedRate = 1000)
    public void recordActiveTracks() {
        Map<String, String> activeTasks = trackRecordRedisUtils.getAllActiveTasks("MAVLINK");
        if (activeTasks.isEmpty()) {
            return;
        }
        log.info("当前MAVLink活动任务数量: {}", activeTasks.size());

        long currentTime = System.currentTimeMillis();

        // 遍历所有执行中的任务
        for (Map.Entry<String, String> entry : activeTasks.entrySet()) {
            String droneId = entry.getKey();
            String taskId = entry.getValue();

            // 注意：不进行超时检查，因为没有使用开始时间

            // 使用分布式锁安全记录轨迹点
            trackRecordRedisUtils.tryRecordTrackPoint("MAVLINK", droneId, MIN_RECORD_INTERVAL, () -> {
                try {
                    // 获取无人机当前状态
                    DroneStatus status = mavlinkCoreService.getDroneStatus(droneId);
                    if (status == null) {
                        log.info("未获取到无人机[{}]的状态数据，跳过记录", droneId);
                        return;
                    }

                    // 转换为轨迹点并保存
                    FlightTrackPointBo trackPoint = convertToTrackPoint(taskId, status);
                    flightTrackService.saveTrackPoint(trackPoint);

                    log.info("已记录无人机[{}]的轨迹点，任务ID：{}", droneId, taskId);
                } catch (Exception e) {
                    log.error("记录无人机[{}]轨迹点失败", droneId, e);
                    throw new RuntimeException(e);  // 让分布式锁机制处理异常
                }
            });
        }
    }

    /**
     * 将无人机状态转换为轨迹点
     *
     * @param taskId 任务ID
     * @param status 无人机状态
     * @return 轨迹点业务对象
     */
    private FlightTrackPointBo convertToTrackPoint(String taskId, DroneStatus status) {
        FlightTrackPointBo trackPoint = new FlightTrackPointBo();

        // 设置基本信息
        trackPoint.setTaskId(taskId);
        trackPoint.setDroneId(status.getDroneSN());
        trackPoint.setTimestamp(status.getLastUpdateTime());

        // 设置位置信息
        if (status.getLatitude() != null) {
            trackPoint.setLatitude(status.getLatitude().doubleValue());
        }
        if (status.getLongitude() != null) {
            trackPoint.setLongitude(status.getLongitude().doubleValue());
        }
        trackPoint.setAltitude((double)status.getAbsoluteAltitude());
        trackPoint.setRelativeHeight((double)status.getRelativeAltitude());

        // 设置飞行信息
        trackPoint.setSpeed(status.getSpeed());
        trackPoint.setGroundSpeed((double)status.getGroundSpeed());
        trackPoint.setAirSpeed(status.getAirSpeed());
        trackPoint.setFlightDistance(status.getFlightDistance());
        trackPoint.setHeading((double)status.getHeading());
        trackPoint.setPitch((double)status.getPitch());
        trackPoint.setRoll((double)status.getRoll());
        trackPoint.setFlightMode(status.getFlightMode());

        // 设置RTK信息
        trackPoint.setRtkAltitude(status.getRtkAltitude());
        trackPoint.setIsRTKConnected(status.isRTKConnected());
        trackPoint.setIsRTKEnabled(status.isRTKEnabled());
        trackPoint.setIsRTKHealthy(status.isRTKHealthy());
        trackPoint.setRtkSatelliteCount(status.getRtkSatelliteCount());

        // 设置任务状态
        trackPoint.setMissionStatus(status.getCurrentWaypoint() > 0 ? "EXECUTING" : "IDLE");

        // 设置电池信息（如果有）
        if (status.getBatteryInfo() != null) {
            FlightTrackPointBo.BatteryInfo batteryInfo = new FlightTrackPointBo.BatteryInfo();
            batteryInfo.setChargeRemainingInPercent(status.getBatteryInfo().getChargeRemainingInPercent());
            trackPoint.setBatteryInfo(batteryInfo);
        }

        return trackPoint;
    }
}