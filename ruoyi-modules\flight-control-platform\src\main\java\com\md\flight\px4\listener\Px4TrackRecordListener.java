package com.md.flight.px4.listener;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.constant.MqttConstants;
import com.md.domain.bo.FlightTrackPointBo;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTrackService;
import com.md.utils.TrackRecordRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * PX4轨迹记录监听器
 * 监听PX4无人机状态变化，在任务执行期间将轨迹数据保存到MongoDB
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Px4TrackRecordListener {

    private final IFlightTrackService flightTrackService;
    private final TrackRecordRedisUtils trackRecordRedisUtils;

    // 最小记录间隔（毫秒）
    private static final long MIN_RECORD_INTERVAL = 950;

    // 任务超时时间（毫秒）- 2小时
    private static final long TASK_TIMEOUT = 2 * 60 * 60 * 1000;

    /**
     * 监听任务执行事件（仅处理PX4类型的事件）
     *
     * @param event 任务执行事件
     */
    @EventListener
    public void onTaskExecutionEvent(TaskExecutionEvent event) {
        // 只处理PX4类型的事件
        if (event.getDroneType() != DroneType.PX4) {
            log.debug("忽略非PX4类型的任务执行事件: droneId={}, droneType={}", event.getDroneId(),
                event.getDroneType());
            return;
        }

        String droneId = event.getDroneId();
        String taskId = event.getTaskId();
        boolean isExecuting = event.isExecuting();

        log.info("收到PX4任务执行事件: droneId={}, taskId={}, isExecuting={}, droneType={}", droneId, taskId,
            isExecuting, event.getDroneType());

        if (isExecuting) {
            // 任务开始执行，记录任务ID
            trackRecordRedisUtils.setActiveTask("PX4", droneId, taskId);
            trackRecordRedisUtils.setTaskStartTime("PX4", droneId, System.currentTimeMillis());
            log.info("开始记录PX4无人机[{}]的轨迹，任务ID：{}", droneId, taskId);
        } else {
            // 任务结束，移除记录
            trackRecordRedisUtils.cleanupTaskData("PX4", droneId);
            log.info("停止记录PX4无人机[{}]的轨迹", droneId);
        }
    }

    /**
     * 定期检查活动任务，记录轨迹点
     * 每秒执行一次，检查所有执行中的任务
     */
    @Scheduled(fixedRate = 1000)
    public void recordActiveTracks() {
        Map<String, String> activeTasks = trackRecordRedisUtils.getAllActiveTasks("PX4");
        if (activeTasks.isEmpty()) {
            return;
        }

        log.info("当前PX4活动任务数量: {}", activeTasks.size());
        long currentTime = System.currentTimeMillis();

        // 遍历所有执行中的任务
        for (Map.Entry<String, String> entry : activeTasks.entrySet()) {
            String droneId = entry.getKey();
            String taskId = entry.getValue();

            // 检查任务是否超时
            if (trackRecordRedisUtils.isTaskTimeout("PX4", droneId, TASK_TIMEOUT)) {
                log.warn("PX4任务执行超时，自动停止轨迹记录: droneId={}, taskId={}", droneId, taskId);
                trackRecordRedisUtils.cleanupTaskData("PX4", droneId);
                continue;
            }

            // 使用分布式锁安全记录轨迹点
            trackRecordRedisUtils.tryRecordTrackPoint("PX4", droneId, MIN_RECORD_INTERVAL, () -> {
                try {
                    // 从Redis获取PX4无人机当前状态
                    String statusData = TenantHelper.ignore(
                        () -> RedisUtils.getCacheObject(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId));

                    if (statusData == null) {
                        log.warn("未获取到PX4无人机[{}]的状态数据，跳过记录。Redis键: {}", droneId,
                            MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId);
                        return;
                    }

                    JSONObject statusJson = JSON.parseObject(statusData);

                    // 转换为轨迹点并保存
                    FlightTrackPointBo trackPoint = convertToTrackPoint(taskId, droneId, statusJson);
                    flightTrackService.saveTrackPoint(trackPoint);

                    log.info("已记录PX4无人机[{}]的轨迹点，任务ID：{}", droneId, taskId);
                } catch (Exception e) {
                    log.error("记录PX4无人机[{}]轨迹点失败", droneId, e);
                    throw new RuntimeException(e);  // 让分布式锁机制处理异常
                }
            });
        }
    }

    /**
     * 将PX4无人机状态转换为轨迹点
     *
     * @param taskId     任务ID
     * @param droneId    无人机ID
     * @param statusJson 状态JSON数据（DroneStatus格式）
     * @return 轨迹点业务对象
     */
    private FlightTrackPointBo convertToTrackPoint(String taskId, String droneId, JSONObject statusJson) {
        // 将JSON转换为DroneStatus对象
        DroneStatus status = JSON.parseObject(statusJson.toJSONString(), DroneStatus.class);
        return convertToTrackPoint(taskId, status);
    }

    /**
     * 将DroneStatus转换为轨迹点（参考MavlinkTrackRecordListener的实现）
     *
     * @param taskId 任务ID
     * @param status DroneStatus对象
     * @return 轨迹点业务对象
     */
    private FlightTrackPointBo convertToTrackPoint(String taskId, DroneStatus status) {
        FlightTrackPointBo trackPoint = new FlightTrackPointBo();

        // 设置基本信息
        trackPoint.setTaskId(taskId);
        trackPoint.setDroneId(status.getDroneSN());
        trackPoint.setTimestamp(status.getLastUpdateTime() != 0 ? status.getLastUpdateTime() : status.getTimestamp());

        // 设置位置信息
        if (status.getLatitude() != null) {
            trackPoint.setLatitude(status.getLatitude().doubleValue());
        }
        if (status.getLongitude() != null) {
            trackPoint.setLongitude(status.getLongitude().doubleValue());
        }
        trackPoint.setAltitude((double)status.getAbsoluteAltitude());
        trackPoint.setRelativeHeight((double)status.getRelativeAltitude());

        // 设置速度和飞行信息
        trackPoint.setSpeed(status.getSpeed());
        trackPoint.setAirSpeed(status.getAirSpeed());
        trackPoint.setGroundSpeed((double)status.getGroundSpeed());
        trackPoint.setFlightDistance(status.getFlightDistance());

        // 设置姿态信息
        trackPoint.setHeading((double)status.getHeading());
        trackPoint.setPitch((double)status.getPitch());
        trackPoint.setRoll((double)status.getRoll());

        // 设置飞行状态
        trackPoint.setFlightMode(status.getFlightMode());

        // 设置RTK信息
        trackPoint.setRtkAltitude(status.getRtkAltitude());
        trackPoint.setIsRTKConnected(status.isRTKConnected());
        trackPoint.setIsRTKEnabled(status.isRTKEnabled());
        trackPoint.setIsRTKHealthy(status.isRTKHealthy());
        trackPoint.setRtkSatelliteCount(status.getRtkSatelliteCount());

        // 设置任务状态
        trackPoint.setMissionStatus(status.getCurrentWaypoint() > 0 ? "EXECUTING" : "IDLE");

        // 设置电池信息
        if (status.getBatteryInfo() != null) {
            FlightTrackPointBo.BatteryInfo batteryInfo = new FlightTrackPointBo.BatteryInfo();
            batteryInfo.setTotalCapacity(status.getBatteryInfo().getTotalCapacity());
            batteryInfo.setChargeRemaining(status.getBatteryInfo().getChargeRemaining());
            batteryInfo.setChargeRemainingInPercent(status.getBatteryInfo().getChargeRemainingInPercent());
            trackPoint.setBatteryInfo(batteryInfo);
        }

        // 设置卫星信息
        if (status.getSatelliteInfo() != null) {
            FlightTrackPointBo.SatelliteInfo satInfo = new FlightTrackPointBo.SatelliteInfo();
            satInfo.setGpsCount(status.getSatelliteInfo().getGpsCount());
            satInfo.setBaseStationCount(status.getSatelliteInfo().getBaseStationCount());
            satInfo.setMobileStation1Count(status.getSatelliteInfo().getMobileStation1Count());
            satInfo.setMobileStation2Count(status.getSatelliteInfo().getMobileStation2Count());
            trackPoint.setSatelliteInfo(satInfo);
        } else {
            // 如果没有卫星信息对象，使用基础GPS信息
            FlightTrackPointBo.SatelliteInfo satInfo = new FlightTrackPointBo.SatelliteInfo();
            satInfo.setGpsCount(status.getGpsSatellites());
            satInfo.setBaseStationCount(0);
            satInfo.setMobileStation1Count(0);
            satInfo.setMobileStation2Count(0);
            trackPoint.setSatelliteInfo(satInfo);
        }

        return trackPoint;
    }

}
