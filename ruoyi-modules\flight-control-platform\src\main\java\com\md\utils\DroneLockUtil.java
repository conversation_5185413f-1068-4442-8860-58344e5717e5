package com.md.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 无人机控制锁工具类
 */
@Component
@Slf4j
public class DroneLockUtil {
    private static final String LOCK_PREFIX = "drone:lock:";
    private static final Integer LOCK_EXPIRE_TIME = 15 * 60; // 15分钟

    /**
     * 尝试获取无人机控制权
     *
     * @param uavCode    无人机编号
     * @param operatorId 操作员ID
     * @return 是否获取成功
     */
    public boolean tryLock(String uavCode, Long operatorId) {
        String lockKey = LOCK_PREFIX + uavCode;
        String lockValue = operatorId + ":" + System.currentTimeMillis();

        try {
            // 先尝试获取，如果已存在则检查是否是当前操作员的锁
            Object existValue = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey));
            if (existValue != null) {
                // 检查锁的持有者是否为当前操作员
                String existValueStr = existValue.toString();
                if (existValueStr.startsWith(operatorId + ":")) {
                    // 是当前操作员的锁，刷新过期时间
                    TenantHelper.ignore(() -> {
                        RedisUtils.setCacheObject(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
                        return null;
                    });
                    log.debug("操作员[{}]已持有无人机[{}]的控制权，刷新过期时间", operatorId, uavCode);
                    return true;
                }
                return false;  // 不是当前操作员的锁，返回失败
            }

            // 不存在则设置值和过期时间
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheObject(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
                return null;
            });

            // 再次检查，确保是我们设置的值（处理并发情况）
            String currentValue = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey));
            boolean locked = lockValue.equals(currentValue);

            if (locked) {
                log.info("操作员[{}]成功获取无人机[{}]的控制权", operatorId, uavCode);
            }
            return locked;
        } catch (Exception e) {
            log.error("获取无人机控制锁失败", e);
            return false;
        }
    }

    /**
     * 释放无人机控制权
     *
     * @param uavCode 无人机编号
     */
    public void unlock(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        TenantHelper.ignore(() -> {
            RedisUtils.deleteObject(lockKey);
            return null;
        });
        log.info("无人机[{}]的控制权已释放", uavCode);
    }

    /**
     * 检查无人机是否被锁定
     *
     * @param uavCode 无人机编号
     * @return 是否被锁定
     */
    public boolean isLocked(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey) != null);
    }

    /**
     * 获取当前锁定操作员ID
     *
     * @param uavCode 无人机编号
     * @return 操作员ID，如果未锁定则返回null
     */
    public Long getLockedOperatorId(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        String value = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey));
        if (value != null) {
            return Long.parseLong(value.split(":")[0]);
        }
        return null;
    }
}